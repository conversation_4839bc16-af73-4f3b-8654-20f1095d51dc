import { AnthropicError } from "../../error.mjs";
import { readableStreamAsyncIterable } from "../../streaming.mjs";
import { LineDecoder } from "./line.mjs";
export class JSONLDecoder {
    constructor(iterator, controller) {
        this.iterator = iterator;
        this.controller = controller;
    }
    async *decoder() {
        const lineDecoder = new LineDecoder();
        for await (const chunk of this.iterator) {
            for (const line of lineDecoder.decode(chunk)) {
                yield JSON.parse(line);
            }
        }
        for (const line of lineDecoder.flush()) {
            yield JSON.parse(line);
        }
    }
    [Symbol.asyncIterator]() {
        return this.decoder();
    }
    static fromResponse(response, controller) {
        if (!response.body) {
            controller.abort();
            throw new AnthropicError(`Attempted to iterate over a response with no body`);
        }
        return new JSONLDecoder(readableStreamAsyncIterable(response.body), controller);
    }
}
//# sourceMappingURL=jsonl.mjs.map