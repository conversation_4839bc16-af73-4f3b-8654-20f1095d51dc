"use strict";
var __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return (kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;
};
var __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var _PromptCachingBetaMessageStream_instances, _PromptCachingBetaMessageStream_currentMessageSnapshot, _PromptCachingBetaMessageStream_connectedPromise, _PromptCachingBetaMessageStream_resolveConnectedPromise, _PromptCachingBetaMessageStream_rejectConnectedPromise, _PromptCachingBetaMessageStream_endPromise, _PromptCachingBetaMessageStream_resolveEndPromise, _PromptCachingBetaMessageStream_rejectEndPromise, _PromptCachingBetaMessageStream_listeners, _PromptCachingBetaMessageStream_ended, _PromptCachingBetaMessageStream_errored, _PromptCachingBetaMessageStream_aborted, _PromptCachingBetaMessageStream_catchingPromiseCreated, _PromptCachingBetaMessageStream_getFinalMessage, _PromptCachingBetaMessageStream_getFinalText, _PromptCachingBetaMessageStream_handleError, _PromptCachingBetaMessageStream_beginRequest, _PromptCachingBetaMessageStream_addStreamEvent, _PromptCachingBetaMessageStream_endRequest, _PromptCachingBetaMessageStream_accumulateMessage;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PromptCachingBetaMessageStream = void 0;
const error_1 = require("@anthropic-ai/sdk/error");
const streaming_1 = require("@anthropic-ai/sdk/streaming");
const parser_1 = require("../_vendor/partial-json-parser/parser.js");
const JSON_BUF_PROPERTY = '__json_buf';
class PromptCachingBetaMessageStream {
    constructor() {
        _PromptCachingBetaMessageStream_instances.add(this);
        this.messages = [];
        this.receivedMessages = [];
        _PromptCachingBetaMessageStream_currentMessageSnapshot.set(this, void 0);
        this.controller = new AbortController();
        _PromptCachingBetaMessageStream_connectedPromise.set(this, void 0);
        _PromptCachingBetaMessageStream_resolveConnectedPromise.set(this, () => { });
        _PromptCachingBetaMessageStream_rejectConnectedPromise.set(this, () => { });
        _PromptCachingBetaMessageStream_endPromise.set(this, void 0);
        _PromptCachingBetaMessageStream_resolveEndPromise.set(this, () => { });
        _PromptCachingBetaMessageStream_rejectEndPromise.set(this, () => { });
        _PromptCachingBetaMessageStream_listeners.set(this, {});
        _PromptCachingBetaMessageStream_ended.set(this, false);
        _PromptCachingBetaMessageStream_errored.set(this, false);
        _PromptCachingBetaMessageStream_aborted.set(this, false);
        _PromptCachingBetaMessageStream_catchingPromiseCreated.set(this, false);
        _PromptCachingBetaMessageStream_handleError.set(this, (error) => {
            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_errored, true, "f");
            if (error instanceof Error && error.name === 'AbortError') {
                error = new error_1.APIUserAbortError();
            }
            if (error instanceof error_1.APIUserAbortError) {
                __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_aborted, true, "f");
                return this._emit('abort', error);
            }
            if (error instanceof error_1.AnthropicError) {
                return this._emit('error', error);
            }
            if (error instanceof Error) {
                const anthropicError = new error_1.AnthropicError(error.message);
                // @ts-ignore
                anthropicError.cause = error;
                return this._emit('error', anthropicError);
            }
            return this._emit('error', new error_1.AnthropicError(String(error)));
        });
        __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_connectedPromise, new Promise((resolve, reject) => {
            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_resolveConnectedPromise, resolve, "f");
            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_rejectConnectedPromise, reject, "f");
        }), "f");
        __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_endPromise, new Promise((resolve, reject) => {
            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_resolveEndPromise, resolve, "f");
            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_rejectEndPromise, reject, "f");
        }), "f");
        // Don't let these promises cause unhandled rejection errors.
        // we will manually cause an unhandled rejection error later
        // if the user hasn't registered any error listener or called
        // any promise-returning method.
        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_connectedPromise, "f").catch(() => { });
        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_endPromise, "f").catch(() => { });
    }
    /**
     * Intended for use on the frontend, consuming a stream produced with
     * `.toReadableStream()` on the backend.
     *
     * Note that messages sent to the model do not appear in `.on('message')`
     * in this context.
     */
    static fromReadableStream(stream) {
        const runner = new PromptCachingBetaMessageStream();
        runner._run(() => runner._fromReadableStream(stream));
        return runner;
    }
    static createMessage(messages, params, options) {
        const runner = new PromptCachingBetaMessageStream();
        for (const message of params.messages) {
            runner._addPromptCachingBetaMessageParam(message);
        }
        runner._run(() => runner._createPromptCachingBetaMessage(messages, { ...params, stream: true }, { ...options, headers: { ...options?.headers, 'X-Stainless-Helper-Method': 'stream' } }));
        return runner;
    }
    _run(executor) {
        executor().then(() => {
            this._emitFinal();
            this._emit('end');
        }, __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_handleError, "f"));
    }
    _addPromptCachingBetaMessageParam(message) {
        this.messages.push(message);
    }
    _addPromptCachingBetaMessage(message, emit = true) {
        this.receivedMessages.push(message);
        if (emit) {
            this._emit('message', message);
        }
    }
    async _createPromptCachingBetaMessage(messages, params, options) {
        const signal = options?.signal;
        if (signal) {
            if (signal.aborted)
                this.controller.abort();
            signal.addEventListener('abort', () => this.controller.abort());
        }
        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, "m", _PromptCachingBetaMessageStream_beginRequest).call(this);
        const stream = await messages.create({ ...params, stream: true }, { ...options, signal: this.controller.signal });
        this._connected();
        for await (const event of stream) {
            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, "m", _PromptCachingBetaMessageStream_addStreamEvent).call(this, event);
        }
        if (stream.controller.signal?.aborted) {
            throw new error_1.APIUserAbortError();
        }
        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, "m", _PromptCachingBetaMessageStream_endRequest).call(this);
    }
    _connected() {
        if (this.ended)
            return;
        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_resolveConnectedPromise, "f").call(this);
        this._emit('connect');
    }
    get ended() {
        return __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_ended, "f");
    }
    get errored() {
        return __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_errored, "f");
    }
    get aborted() {
        return __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_aborted, "f");
    }
    abort() {
        this.controller.abort();
    }
    /**
     * Adds the listener function to the end of the listeners array for the event.
     * No checks are made to see if the listener has already been added. Multiple calls passing
     * the same combination of event and listener will result in the listener being added, and
     * called, multiple times.
     * @returns this PromptCachingBetaMessageStream, so that calls can be chained
     */
    on(event, listener) {
        const listeners = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, "f")[event] || (__classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, "f")[event] = []);
        listeners.push({ listener });
        return this;
    }
    /**
     * Removes the specified listener from the listener array for the event.
     * off() will remove, at most, one instance of a listener from the listener array. If any single
     * listener has been added multiple times to the listener array for the specified event, then
     * off() must be called multiple times to remove each instance.
     * @returns this PromptCachingBetaMessageStream, so that calls can be chained
     */
    off(event, listener) {
        const listeners = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, "f")[event];
        if (!listeners)
            return this;
        const index = listeners.findIndex((l) => l.listener === listener);
        if (index >= 0)
            listeners.splice(index, 1);
        return this;
    }
    /**
     * Adds a one-time listener function for the event. The next time the event is triggered,
     * this listener is removed and then invoked.
     * @returns this PromptCachingBetaMessageStream, so that calls can be chained
     */
    once(event, listener) {
        const listeners = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, "f")[event] || (__classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, "f")[event] = []);
        listeners.push({ listener, once: true });
        return this;
    }
    /**
     * This is similar to `.once()`, but returns a Promise that resolves the next time
     * the event is triggered, instead of calling a listener callback.
     * @returns a Promise that resolves the next time given event is triggered,
     * or rejects if an error is emitted.  (If you request the 'error' event,
     * returns a promise that resolves with the error).
     *
     * Example:
     *
     *   const message = await stream.emitted('message') // rejects if the stream errors
     */
    emitted(event) {
        return new Promise((resolve, reject) => {
            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_catchingPromiseCreated, true, "f");
            if (event !== 'error')
                this.once('error', reject);
            this.once(event, resolve);
        });
    }
    async done() {
        __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_catchingPromiseCreated, true, "f");
        await __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_endPromise, "f");
    }
    get currentMessage() {
        return __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_currentMessageSnapshot, "f");
    }
    /**
     * @returns a promise that resolves with the the final assistant PromptCachingBetaMessage response,
     * or rejects if an error occurred or the stream ended prematurely without producing a PromptCachingBetaMessage.
     */
    async finalMessage() {
        await this.done();
        return __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, "m", _PromptCachingBetaMessageStream_getFinalMessage).call(this);
    }
    /**
     * @returns a promise that resolves with the the final assistant PromptCachingBetaMessage's text response, concatenated
     * together if there are more than one text blocks.
     * Rejects if an error occurred or the stream ended prematurely without producing a PromptCachingBetaMessage.
     */
    async finalText() {
        await this.done();
        return __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, "m", _PromptCachingBetaMessageStream_getFinalText).call(this);
    }
    _emit(event, ...args) {
        // make sure we don't emit any PromptCachingBetaMessageStreamEvents after end
        if (__classPrivateFieldGet(this, _PromptCachingBetaMessageStream_ended, "f"))
            return;
        if (event === 'end') {
            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_ended, true, "f");
            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_resolveEndPromise, "f").call(this);
        }
        const listeners = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, "f")[event];
        if (listeners) {
            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, "f")[event] = listeners.filter((l) => !l.once);
            listeners.forEach(({ listener }) => listener(...args));
        }
        if (event === 'abort') {
            const error = args[0];
            if (!__classPrivateFieldGet(this, _PromptCachingBetaMessageStream_catchingPromiseCreated, "f") && !listeners?.length) {
                Promise.reject(error);
            }
            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_rejectConnectedPromise, "f").call(this, error);
            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_rejectEndPromise, "f").call(this, error);
            this._emit('end');
            return;
        }
        if (event === 'error') {
            // NOTE: _emit('error', error) should only be called from #handleError().
            const error = args[0];
            if (!__classPrivateFieldGet(this, _PromptCachingBetaMessageStream_catchingPromiseCreated, "f") && !listeners?.length) {
                // Trigger an unhandled rejection if the user hasn't registered any error handlers.
                // If you are seeing stack traces here, make sure to handle errors via either:
                // - runner.on('error', () => ...)
                // - await runner.done()
                // - await runner.final...()
                // - etc.
                Promise.reject(error);
            }
            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_rejectConnectedPromise, "f").call(this, error);
            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_rejectEndPromise, "f").call(this, error);
            this._emit('end');
        }
    }
    _emitFinal() {
        const finalPromptCachingBetaMessage = this.receivedMessages.at(-1);
        if (finalPromptCachingBetaMessage) {
            this._emit('finalPromptCachingBetaMessage', __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, "m", _PromptCachingBetaMessageStream_getFinalMessage).call(this));
        }
    }
    async _fromReadableStream(readableStream, options) {
        const signal = options?.signal;
        if (signal) {
            if (signal.aborted)
                this.controller.abort();
            signal.addEventListener('abort', () => this.controller.abort());
        }
        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, "m", _PromptCachingBetaMessageStream_beginRequest).call(this);
        this._connected();
        const stream = streaming_1.Stream.fromReadableStream(readableStream, this.controller);
        for await (const event of stream) {
            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, "m", _PromptCachingBetaMessageStream_addStreamEvent).call(this, event);
        }
        if (stream.controller.signal?.aborted) {
            throw new error_1.APIUserAbortError();
        }
        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, "m", _PromptCachingBetaMessageStream_endRequest).call(this);
    }
    [(_PromptCachingBetaMessageStream_currentMessageSnapshot = new WeakMap(), _PromptCachingBetaMessageStream_connectedPromise = new WeakMap(), _PromptCachingBetaMessageStream_resolveConnectedPromise = new WeakMap(), _PromptCachingBetaMessageStream_rejectConnectedPromise = new WeakMap(), _PromptCachingBetaMessageStream_endPromise = new WeakMap(), _PromptCachingBetaMessageStream_resolveEndPromise = new WeakMap(), _PromptCachingBetaMessageStream_rejectEndPromise = new WeakMap(), _PromptCachingBetaMessageStream_listeners = new WeakMap(), _PromptCachingBetaMessageStream_ended = new WeakMap(), _PromptCachingBetaMessageStream_errored = new WeakMap(), _PromptCachingBetaMessageStream_aborted = new WeakMap(), _PromptCachingBetaMessageStream_catchingPromiseCreated = new WeakMap(), _PromptCachingBetaMessageStream_handleError = new WeakMap(), _PromptCachingBetaMessageStream_instances = new WeakSet(), _PromptCachingBetaMessageStream_getFinalMessage = function _PromptCachingBetaMessageStream_getFinalMessage() {
        if (this.receivedMessages.length === 0) {
            throw new error_1.AnthropicError('stream ended without producing a PromptCachingBetaMessage with role=assistant');
        }
        return this.receivedMessages.at(-1);
    }, _PromptCachingBetaMessageStream_getFinalText = function _PromptCachingBetaMessageStream_getFinalText() {
        if (this.receivedMessages.length === 0) {
            throw new error_1.AnthropicError('stream ended without producing a PromptCachingBetaMessage with role=assistant');
        }
        const textBlocks = this.receivedMessages
            .at(-1)
            .content.filter((block) => block.type === 'text')
            .map((block) => block.text);
        if (textBlocks.length === 0) {
            throw new error_1.AnthropicError('stream ended without producing a content block with type=text');
        }
        return textBlocks.join(' ');
    }, _PromptCachingBetaMessageStream_beginRequest = function _PromptCachingBetaMessageStream_beginRequest() {
        if (this.ended)
            return;
        __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_currentMessageSnapshot, undefined, "f");
    }, _PromptCachingBetaMessageStream_addStreamEvent = function _PromptCachingBetaMessageStream_addStreamEvent(event) {
        if (this.ended)
            return;
        const messageSnapshot = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, "m", _PromptCachingBetaMessageStream_accumulateMessage).call(this, event);
        this._emit('streamEvent', event, messageSnapshot);
        switch (event.type) {
            case 'content_block_delta': {
                const content = messageSnapshot.content.at(-1);
                if (event.delta.type === 'text_delta' && content.type === 'text') {
                    this._emit('text', event.delta.text, content.text || '');
                }
                else if (event.delta.type === 'input_json_delta' && content.type === 'tool_use') {
                    if (content.input) {
                        this._emit('inputJson', event.delta.partial_json, content.input);
                    }
                }
                break;
            }
            case 'message_stop': {
                this._addPromptCachingBetaMessageParam(messageSnapshot);
                this._addPromptCachingBetaMessage(messageSnapshot, true);
                break;
            }
            case 'content_block_stop': {
                this._emit('contentBlock', messageSnapshot.content.at(-1));
                break;
            }
            case 'message_start': {
                __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_currentMessageSnapshot, messageSnapshot, "f");
                break;
            }
            case 'content_block_start':
            case 'message_delta':
                break;
        }
    }, _PromptCachingBetaMessageStream_endRequest = function _PromptCachingBetaMessageStream_endRequest() {
        if (this.ended) {
            throw new error_1.AnthropicError(`stream has ended, this shouldn't happen`);
        }
        const snapshot = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_currentMessageSnapshot, "f");
        if (!snapshot) {
            throw new error_1.AnthropicError(`request ended without sending any chunks`);
        }
        __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_currentMessageSnapshot, undefined, "f");
        return snapshot;
    }, _PromptCachingBetaMessageStream_accumulateMessage = function _PromptCachingBetaMessageStream_accumulateMessage(event) {
        let snapshot = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_currentMessageSnapshot, "f");
        if (event.type === 'message_start') {
            if (snapshot) {
                throw new error_1.AnthropicError(`Unexpected event order, got ${event.type} before receiving "message_stop"`);
            }
            return event.message;
        }
        if (!snapshot) {
            throw new error_1.AnthropicError(`Unexpected event order, got ${event.type} before "message_start"`);
        }
        switch (event.type) {
            case 'message_stop':
                return snapshot;
            case 'message_delta':
                snapshot.stop_reason = event.delta.stop_reason;
                snapshot.stop_sequence = event.delta.stop_sequence;
                snapshot.usage.output_tokens = event.usage.output_tokens;
                return snapshot;
            case 'content_block_start':
                snapshot.content.push(event.content_block);
                return snapshot;
            case 'content_block_delta': {
                const snapshotContent = snapshot.content.at(event.index);
                if (snapshotContent?.type === 'text' && event.delta.type === 'text_delta') {
                    snapshotContent.text += event.delta.text;
                }
                else if (snapshotContent?.type === 'tool_use' && event.delta.type === 'input_json_delta') {
                    // we need to keep track of the raw JSON string as well so that we can
                    // re-parse it for each delta, for now we just store it as an untyped
                    // non-enumerable property on the snapshot
                    let jsonBuf = snapshotContent[JSON_BUF_PROPERTY] || '';
                    jsonBuf += event.delta.partial_json;
                    Object.defineProperty(snapshotContent, JSON_BUF_PROPERTY, {
                        value: jsonBuf,
                        enumerable: false,
                        writable: true,
                    });
                    if (jsonBuf) {
                        snapshotContent.input = (0, parser_1.partialParse)(jsonBuf);
                    }
                }
                return snapshot;
            }
            case 'content_block_stop':
                return snapshot;
        }
    }, Symbol.asyncIterator)]() {
        const pushQueue = [];
        const readQueue = [];
        let done = false;
        this.on('streamEvent', (event) => {
            const reader = readQueue.shift();
            if (reader) {
                reader.resolve(event);
            }
            else {
                pushQueue.push(event);
            }
        });
        this.on('end', () => {
            done = true;
            for (const reader of readQueue) {
                reader.resolve(undefined);
            }
            readQueue.length = 0;
        });
        this.on('abort', (err) => {
            done = true;
            for (const reader of readQueue) {
                reader.reject(err);
            }
            readQueue.length = 0;
        });
        this.on('error', (err) => {
            done = true;
            for (const reader of readQueue) {
                reader.reject(err);
            }
            readQueue.length = 0;
        });
        return {
            next: async () => {
                if (!pushQueue.length) {
                    if (done) {
                        return { value: undefined, done: true };
                    }
                    return new Promise((resolve, reject) => readQueue.push({ resolve, reject })).then((chunk) => (chunk ? { value: chunk, done: false } : { value: undefined, done: true }));
                }
                const chunk = pushQueue.shift();
                return { value: chunk, done: false };
            },
            return: async () => {
                this.abort();
                return { value: undefined, done: true };
            },
        };
    }
    toReadableStream() {
        const stream = new streaming_1.Stream(this[Symbol.asyncIterator].bind(this), this.controller);
        return stream.toReadableStream();
    }
}
exports.PromptCachingBetaMessageStream = PromptCachingBetaMessageStream;
//# sourceMappingURL=PromptCachingBetaMessageStream.js.map