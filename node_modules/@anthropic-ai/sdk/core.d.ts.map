{"version": 3, "file": "core.d.ts", "sourceRoot": "", "sources": ["src/core.ts"], "names": [], "mappings": ";;AACA,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAEL,QAAQ,EAIT,MAAM,SAAS,CAAC;AACjB,OAAO,EAEL,KAAK,QAAQ,EAEb,KAAK,KAAK,EAEV,KAAK,WAAW,EAChB,KAAK,WAAW,EAChB,KAAK,QAAQ,EACb,KAAK,WAAW,EACjB,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAE,KAAK,QAAQ,EAAE,CAAC;AACzB,OAAO,EAAE,QAAQ,EAA+B,MAAM,WAAW,CAAC;AAClE,OAAO,EACL,gCAAgC,EAChC,2BAA2B,EAC3B,UAAU,EACV,KAAK,UAAU,GAChB,MAAM,WAAW,CAAC;AAEnB,MAAM,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,WAAW,EAAE,IAAI,CAAC,EAAE,WAAW,KAAK,OAAO,CAAC,QAAQ,CAAC,CAAC;AAEhF,KAAK,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAExC,KAAK,gBAAgB,GAAG;IACtB,QAAQ,EAAE,QAAQ,CAAC;IACnB,OAAO,EAAE,mBAAmB,CAAC;IAC7B,UAAU,EAAE,eAAe,CAAC;CAC7B,CAAC;AA4CF;;;GAGG;AACH,qBAAa,UAAU,CAAC,CAAC,CAAE,SAAQ,OAAO,CAAC,CAAC,CAAC;IAIzC,OAAO,CAAC,eAAe;IACvB,OAAO,CAAC,aAAa;IAJvB,OAAO,CAAC,aAAa,CAAyB;gBAGpC,eAAe,EAAE,OAAO,CAAC,gBAAgB,CAAC,EAC1C,aAAa,GAAE,CAAC,KAAK,EAAE,gBAAgB,KAAK,cAAc,CAAC,CAAC,CAAwB;IAU9F,WAAW,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,gBAAgB,KAAK,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;IAMjF;;;;;;;;;;;;OAYG;IACH,UAAU,IAAI,OAAO,CAAC,QAAQ,CAAC;IAG/B;;;;;;;;;;;;OAYG;IACG,YAAY,IAAI,OAAO,CAAC;QAAE,IAAI,EAAE,CAAC,CAAC;QAAC,QAAQ,EAAE,QAAQ,CAAA;KAAE,CAAC;IAK9D,OAAO,CAAC,KAAK;IAOJ,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,KAAK,EAC1C,WAAW,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,EACjF,UAAU,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,GAAG,KAAK,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,GAClF,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAItB,KAAK,CAAC,OAAO,GAAG,KAAK,EAC5B,UAAU,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,GAAG,KAAK,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,GAChF,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC;IAId,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,GAAG,SAAS,GAAG,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC;CAG1E;AAED,8BAAsB,SAAS;IAC7B,OAAO,EAAE,MAAM,CAAC;IAChB,UAAU,EAAE,MAAM,CAAC;IACnB,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,KAAK,GAAG,SAAS,CAAC;IAE7B,OAAO,CAAC,KAAK,CAAQ;IACrB,SAAS,CAAC,iBAAiB,CAAC,EAAE,MAAM,CAAC;gBAEzB,EACV,OAAO,EACP,UAAc,EACd,OAAgB,EAAE,aAAa;IAC/B,SAAS,EACT,KAAK,EAAE,cAAc,GACtB,EAAE;QACD,OAAO,EAAE,MAAM,CAAC;QAChB,UAAU,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;QAChC,OAAO,EAAE,MAAM,GAAG,SAAS,CAAC;QAC5B,SAAS,EAAE,KAAK,GAAG,SAAS,CAAC;QAC7B,KAAK,EAAE,KAAK,GAAG,SAAS,CAAC;KAC1B;IASD,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,mBAAmB,GAAG,OAAO;IAIzD;;;;;;;OAOG;IACH,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,mBAAmB,GAAG,OAAO;IAU5D,SAAS,CAAC,QAAQ,CAAC,YAAY,IAAI,YAAY,GAAG,SAAS;IAE3D;;OAEG;IACH,SAAS,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,OAAO;IAElE,SAAS,CAAC,qBAAqB,IAAI,MAAM;IAIzC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,cAAc,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC;IAIxF,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,cAAc,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC;IAIzF,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,cAAc,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC;IAI1F,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,cAAc,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC;IAIxF,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,cAAc,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC;IAI3F,OAAO,CAAC,aAAa;IAkBrB,UAAU,CAAC,IAAI,EAAE,SAAS,SAAS,YAAY,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,EACxE,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,SAAS,EACvC,IAAI,CAAC,EAAE,cAAc,CAAC,GAAG,CAAC,GACzB,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC;IAI/B,OAAO,CAAC,sBAAsB;IAkB9B,YAAY,CAAC,GAAG,EACd,OAAO,EAAE,mBAAmB,CAAC,GAAG,CAAC,EACjC,EAAE,UAAc,EAAE,GAAE;QAAE,UAAU,CAAC,EAAE,MAAM,CAAA;KAAO,GAC/C;QAAE,GAAG,EAAE,WAAW,CAAC;QAAC,GAAG,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,MAAM,CAAA;KAAE;IA+CrD,OAAO,CAAC,YAAY;IAwCpB;;OAEG;cACa,cAAc,CAAC,OAAO,EAAE,mBAAmB,GAAG,OAAO,CAAC,IAAI,CAAC;IAE3E;;;;;OAKG;cACa,cAAc,CAC5B,OAAO,EAAE,WAAW,EACpB,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE;QAAE,GAAG,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,mBAAmB,CAAA;KAAE,GAC9D,OAAO,CAAC,IAAI,CAAC;IAEhB,SAAS,CAAC,YAAY,CAAC,OAAO,EAAE,WAAW,GAAG,IAAI,GAAG,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;IASvF,SAAS,CAAC,eAAe,CACvB,MAAM,EAAE,MAAM,GAAG,SAAS,EAC1B,KAAK,EAAE,MAAM,GAAG,SAAS,EACzB,OAAO,EAAE,MAAM,GAAG,SAAS,EAC3B,OAAO,EAAE,OAAO,GAAG,SAAS,GAC3B,QAAQ;IAIX,OAAO,CAAC,GAAG,EAAE,GAAG,EACd,OAAO,EAAE,cAAc,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,EACjD,gBAAgB,GAAE,MAAM,GAAG,IAAW,GACrC,UAAU,CAAC,GAAG,CAAC;YAIJ,WAAW;IA6DzB,cAAc,CAAC,IAAI,GAAG,OAAO,EAAE,SAAS,SAAS,YAAY,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,EACtF,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,qBAAqB,CAAC,OAAO,YAAY,CAAC,KAAK,SAAS,EAC5E,OAAO,EAAE,mBAAmB,GAC3B,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC;IAK/B,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,IAAI,GAAG,SAAS,GAAG,MAAM;IAkBlE,SAAS,CAAC,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,MAAM;IAiB1D,gBAAgB,CACpB,GAAG,EAAE,WAAW,EAChB,IAAI,EAAE,WAAW,GAAG,SAAS,EAC7B,EAAE,EAAE,MAAM,EACV,UAAU,EAAE,eAAe,GAC1B,OAAO,CAAC,QAAQ,CAAC;IAgBpB,SAAS,CAAC,gBAAgB,IAAI,aAAa;IAI3C,OAAO,CAAC,WAAW;YAuBL,YAAY;IAsC1B,OAAO,CAAC,kCAAkC;IAe1C,OAAO,CAAC,YAAY;CAGrB;AAED,MAAM,MAAM,QAAQ,GAAG;IAAE,GAAG,EAAE,GAAG,CAAA;CAAE,GAAG;IAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,IAAI,CAAA;CAAE,CAAC;AAEjF,8BAAsB,YAAY,CAAC,IAAI,CAAE,YAAW,aAAa,CAAC,IAAI,CAAC;;IAErE,SAAS,CAAC,OAAO,EAAE,mBAAmB,CAAC;IAEvC,SAAS,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAC7B,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC;gBAEZ,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,mBAAmB;IAO9F;;OAEG;IACH,QAAQ,CAAC,cAAc,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,GAAG,IAAI;IAClE,QAAQ,CAAC,YAAY,IAAI,QAAQ,GAAG,IAAI;IAExC,QAAQ,CAAC,iBAAiB,IAAI,IAAI,EAAE;IAEpC,WAAW,IAAI,OAAO;IAMhB,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC;IAqB3B,SAAS,IAAI,cAAc,CAAC,IAAI,CAAC;IAUjC,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC;CAOtD;AAED;;;;;;;;GAQG;AACH,qBAAa,WAAW,CACpB,SAAS,SAAS,YAAY,CAAC,IAAI,CAAC,EACpC,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAAC,MAAM,CAAC,CAE3D,SAAQ,UAAU,CAAC,SAAS,CAC5B,YAAW,aAAa,CAAC,IAAI,CAAC;gBAG5B,MAAM,EAAE,SAAS,EACjB,OAAO,EAAE,OAAO,CAAC,gBAAgB,CAAC,EAClC,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,qBAAqB,CAAC,OAAO,YAAY,CAAC,KAAK,SAAS;IAQ9E;;;;;;OAMG;IACI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC;CAMtD;AAED,eAAO,MAAM,qBAAqB,YACvB,QAAQ,WAAW,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,KAC7C,OAAO,MAAM,EAAE,MAAM,CAavB,CAAC;AAEF,KAAK,UAAU,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,OAAO,GAAG,QAAQ,CAAC;AAE9D,MAAM,MAAM,aAAa,GAAG;IAAE,KAAK,EAAE,KAAK,CAAA;CAAE,CAAC;AAC7C,MAAM,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC,CAAC;AAChE,MAAM,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC,CAAC;AAC9D,MAAM,MAAM,QAAQ,CAAC,CAAC,IAAI;KAAG,CAAC,IAAI,MAAM,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI;CAAE,CAAC;AAE7D,MAAM,MAAM,cAAc,CACxB,GAAG,GAAG,OAAO,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,QAAQ,GAAG,QAAQ,GAAG,eAAe,GAAG,WAAW,IAC3F;IACF,MAAM,CAAC,EAAE,UAAU,CAAC;IACpB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,KAAK,CAAC,EAAE,GAAG,GAAG,SAAS,CAAC;IACxB,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI,GAAG,SAAS,CAAC;IAC9B,OAAO,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAE9B,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,MAAM,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC7B,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,SAAS,CAAC,EAAE,KAAK,CAAC;IAClB,MAAM,CAAC,EAAE,WAAW,GAAG,SAAS,GAAG,IAAI,CAAC;IACxC,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB,eAAe,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IACtC,gBAAgB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IACvC,aAAa,CAAC,EAAE,OAAO,MAAM,CAAC;CAC/B,CAAC;AAwBF,eAAO,MAAM,gBAAgB,QAAS,OAAO,mCAO5C,CAAC;AAEF,MAAM,MAAM,mBAAmB,CAAC,GAAG,GAAG,OAAO,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,QAAQ,GAAG,QAAQ,IAC3F,cAAc,CAAC,GAAG,CAAC,GAAG;IACpB,MAAM,EAAE,UAAU,CAAC;IACnB,IAAI,EAAE,MAAM,CAAC;CACd,CAAC;AAiKJ,eAAO,MAAM,QAAQ,SAAU,MAAM,QAMpC,CAAC;AAQF,eAAO,MAAM,KAAK,OAAQ,MAAM,qBAAsD,CAAC;AAYvF,eAAO,MAAM,WAAW,QAAS,GAAG,KAAG,KAQtC,CAAC;AAEF,eAAO,MAAM,aAAa,uCAGzB,CAAC;AAEF;;;;;;GAMG;AACH,eAAO,MAAM,OAAO,QAAS,MAAM,KAAG,MAAM,GAAG,SAQ9C,CAAC;AAEF,eAAO,MAAM,aAAa,UAAW,OAAO,KAAG,MAK9C,CAAC;AAEF,eAAO,MAAM,WAAW,UAAW,OAAO,KAAG,MAK5C,CAAC;AAEF,eAAO,MAAM,aAAa,UAAW,OAAO,KAAG,OAI9C,CAAC;AAEF,eAAO,MAAM,kBAAkB,UAAW,OAAO,KAAG,MAAM,GAAG,SAK5D,CAAC;AAEF,eAAO,MAAM,gBAAgB,UAAW,OAAO,KAAG,MAAM,GAAG,SAK1D,CAAC;AAEF,eAAO,MAAM,kBAAkB,UAAW,OAAO,KAAG,OAAO,GAAG,SAK7D,CAAC;AAGF,wBAAgB,UAAU,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO,CAIlE;AAGD,wBAAgB,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,OAAO,CAExD;AAwBD,wBAAgB,KAAK,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,QAInD;AAaD,eAAO,MAAM,kBAAkB,eAS9B,CAAC;AAEF,MAAM,WAAW,eAAe;IAC9B,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACpD;AACD,MAAM,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,SAAS,CAAC,GAAG,eAAe,CAAC;AAE1F,eAAO,MAAM,iBAAiB,YAAa,GAAG,+BAE7C,CAAC;AAEF,eAAO,MAAM,iBAAiB,YAAa,WAAW,GAAG,OAAO,UAAU,MAAM,KAAG,MAMlF,CAAC;AAEF,eAAO,MAAM,SAAS,YAAa,WAAW,GAAG,OAAO,UAAU,MAAM,KAAG,MAAM,GAAG,SA2BnF,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,QAAQ,QAAS,MAAM,GAAG,IAAI,GAAG,SAAS,KAAG,MAWzD,CAAC;AAEF,wBAAgB,KAAK,CAAC,GAAG,EAAE,OAAO,GAAG,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAElE"}