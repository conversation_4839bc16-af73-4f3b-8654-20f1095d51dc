{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src/resources/index.ts"], "names": [], "mappings": "AAEA,OAAO,EACL,IAAI,EACJ,KAAK,aAAa,EAClB,KAAK,YAAY,EACjB,KAAK,uBAAuB,EAC5B,KAAK,SAAS,EACd,KAAK,iBAAiB,EACtB,KAAK,uBAAuB,EAC5B,KAAK,iBAAiB,EACtB,KAAK,mBAAmB,EACxB,KAAK,mBAAmB,EACxB,KAAK,kBAAkB,GACxB,MAAM,aAAa,CAAC;AACrB,OAAO,EACL,WAAW,EACX,KAAK,UAAU,EACf,KAAK,sBAAsB,EAC3B,KAAK,kCAAkC,EACvC,KAAK,+BAA+B,GACrC,MAAM,eAAe,CAAC;AACvB,OAAO,EACL,QAAQ,EACR,KAAK,YAAY,EACjB,KAAK,sBAAsB,EAC3B,KAAK,sBAAsB,EAC3B,KAAK,qBAAqB,EAC1B,KAAK,eAAe,EACpB,KAAK,cAAc,EACnB,KAAK,cAAc,EACnB,KAAK,OAAO,EACZ,KAAK,iBAAiB,EACtB,KAAK,iBAAiB,EACtB,KAAK,YAAY,EACjB,KAAK,iBAAiB,EACtB,KAAK,gBAAgB,EACrB,KAAK,kBAAkB,EACvB,KAAK,mBAAmB,EACxB,KAAK,QAAQ,EACb,KAAK,KAAK,EACV,KAAK,yBAAyB,EAC9B,KAAK,yBAAyB,EAC9B,KAAK,wBAAwB,EAC7B,KAAK,oBAAoB,EACzB,KAAK,oBAAoB,EACzB,KAAK,mBAAmB,EACxB,KAAK,qBAAqB,EAC1B,KAAK,SAAS,EACd,KAAK,cAAc,EACnB,KAAK,SAAS,EACd,KAAK,IAAI,EACT,KAAK,UAAU,EACf,KAAK,aAAa,EAClB,KAAK,cAAc,EACnB,KAAK,cAAc,EACnB,KAAK,oBAAoB,EACzB,KAAK,YAAY,EACjB,KAAK,iBAAiB,EACtB,KAAK,KAAK,EACV,KAAK,mBAAmB,EACxB,KAAK,+BAA+B,EACpC,KAAK,4BAA4B,GAClC,MAAM,YAAY,CAAC"}