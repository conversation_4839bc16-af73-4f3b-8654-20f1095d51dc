{"version": 3, "file": "messages.d.ts", "sourceRoot": "", "sources": ["../../../src/resources/beta/prompt-caching/messages.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,KAAK,IAAI,MAAM,eAAe,CAAC;AACtC,OAAO,KAAK,wBAAwB,MAAM,YAAY,CAAC;AACvD,OAAO,KAAK,WAAW,MAAM,gBAAgB,CAAC;AAC9C,OAAO,KAAK,OAAO,MAAM,SAAS,CAAC;AACnC,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,8BAA8B,EAAE,MAAM,6CAA6C,CAAC;AAE7F,qBAAa,QAAS,SAAQ,WAAW;IACvC;;;;;;OAMG;IACH,MAAM,CACJ,MAAM,EAAE,+BAA+B,EACvC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,UAAU,CAAC,wBAAwB,CAAC;IACvC,MAAM,CACJ,MAAM,EAAE,4BAA4B,EACpC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,UAAU,CAAC,MAAM,CAAC,sCAAsC,CAAC,CAAC;IAC7D,MAAM,CACJ,MAAM,EAAE,uBAAuB,EAC/B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,UAAU,CAAC,MAAM,CAAC,sCAAsC,CAAC,GAAG,wBAAwB,CAAC;IAkBxF;;OAEG;IACH,MAAM,CAAC,IAAI,EAAE,mBAAmB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,8BAA8B;CAGjG;AAED,MAAM,MAAM,mBAAmB,GAAG,uBAAuB,CAAC;AAE1D,MAAM,WAAW,sCAAsC;IACrD,IAAI,EAAE,WAAW,CAAC;CACnB;AAED,MAAM,WAAW,gCAAgC;IAC/C,MAAM,EAAE,gCAAgC,CAAC,MAAM,CAAC;IAEhD,IAAI,EAAE,OAAO,CAAC;IAEd,aAAa,CAAC,EAAE,sCAAsC,GAAG,IAAI,CAAC;CAC/D;AAED,yBAAiB,gCAAgC,CAAC;IAChD,UAAiB,MAAM;QACrB,IAAI,EAAE,MAAM,CAAC;QAEb,UAAU,EAAE,YAAY,GAAG,WAAW,GAAG,WAAW,GAAG,YAAY,CAAC;QAEpE,IAAI,EAAE,QAAQ,CAAC;KAChB;CACF;AAED,MAAM,WAAW,wBAAwB;IACvC;;;;OAIG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACH,OAAO,EAAE,KAAK,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;IAEzC;;;;OAIG;IACH,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC;IAEzB;;;;OAIG;IACH,IAAI,EAAE,WAAW,CAAC;IAElB;;;;;;;;;;;;OAYG;IACH,WAAW,EAAE,UAAU,GAAG,YAAY,GAAG,eAAe,GAAG,UAAU,GAAG,IAAI,CAAC;IAE7E;;;;;OAKG;IACH,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IAE7B;;;;OAIG;IACH,IAAI,EAAE,SAAS,CAAC;IAEhB;;;;;;;;;;;;;OAaG;IACH,KAAK,EAAE,sBAAsB,CAAC;CAC/B;AAED,MAAM,WAAW,6BAA6B;IAC5C,OAAO,EACH,MAAM,GACN,KAAK,CACD,+BAA+B,GAC/B,gCAAgC,GAChC,kCAAkC,GAClC,qCAAqC,CACxC,CAAC;IAEN,IAAI,EAAE,MAAM,GAAG,WAAW,CAAC;CAC5B;AAED,MAAM,WAAW,+BAA+B;IAC9C,IAAI,EAAE,MAAM,CAAC;IAEb,IAAI,EAAE,MAAM,CAAC;IAEb,aAAa,CAAC,EAAE,sCAAsC,GAAG,IAAI,CAAC;CAC/D;AAED,MAAM,WAAW,qBAAqB;IACpC;;;;;OAKG;IACH,YAAY,EAAE,qBAAqB,CAAC,WAAW,CAAC;IAEhD;;;;OAIG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb,aAAa,CAAC,EAAE,sCAAsC,GAAG,IAAI,CAAC;IAE9D;;;;;;;OAOG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;CACtB;AAED,yBAAiB,qBAAqB,CAAC;IACrC;;;;;OAKG;IACH,UAAiB,WAAW;QAC1B,IAAI,EAAE,QAAQ,CAAC;QAEf,UAAU,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;QAC5B,CAAC,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC;KACtB;CACF;AAED,MAAM,WAAW,qCAAqC;IACpD,WAAW,EAAE,MAAM,CAAC;IAEpB,IAAI,EAAE,aAAa,CAAC;IAEpB,aAAa,CAAC,EAAE,sCAAsC,GAAG,IAAI,CAAC;IAE9D,OAAO,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,+BAA+B,GAAG,gCAAgC,CAAC,CAAC;IAE7F,QAAQ,CAAC,EAAE,OAAO,CAAC;CACpB;AAED,MAAM,WAAW,kCAAkC;IACjD,EAAE,EAAE,MAAM,CAAC;IAEX,KAAK,EAAE,OAAO,CAAC;IAEf,IAAI,EAAE,MAAM,CAAC;IAEb,IAAI,EAAE,UAAU,CAAC;IAEjB,aAAa,CAAC,EAAE,sCAAsC,GAAG,IAAI,CAAC;CAC/D;AAED,MAAM,WAAW,sBAAsB;IACrC;;OAEG;IACH,2BAA2B,EAAE,MAAM,GAAG,IAAI,CAAC;IAE3C;;OAEG;IACH,uBAAuB,EAAE,MAAM,GAAG,IAAI,CAAC;IAEvC;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,aAAa,EAAE,MAAM,CAAC;CACvB;AAED,MAAM,WAAW,qCAAqC;IACpD,OAAO,EAAE,wBAAwB,CAAC;IAElC,IAAI,EAAE,eAAe,CAAC;CACvB;AAED,MAAM,MAAM,sCAAsC,GAC9C,qCAAqC,GACrC,WAAW,CAAC,oBAAoB,GAChC,WAAW,CAAC,mBAAmB,GAC/B,WAAW,CAAC,yBAAyB,GACrC,WAAW,CAAC,yBAAyB,GACrC,WAAW,CAAC,wBAAwB,CAAC;AAEzC,MAAM,MAAM,mBAAmB,GAAG,+BAA+B,GAAG,4BAA4B,CAAC;AAEjG,MAAM,WAAW,uBAAuB;IACtC;;;;;;;;OAQG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuFG;IACH,QAAQ,EAAE,KAAK,CAAC,6BAA6B,CAAC,CAAC;IAE/C;;;;OAIG;IACH,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC;IAEzB;;OAEG;IACH,QAAQ,CAAC,EAAE,WAAW,CAAC,QAAQ,CAAC;IAEhC;;;;;;;;;;OAUG;IACH,cAAc,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAE/B;;;;;;OAMG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IAEjB;;;;;;OAMG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,+BAA+B,CAAC,CAAC;IAEzD;;;;;;;;;OASG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;OAGG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC,UAAU,CAAC;IAErC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAqEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,qBAAqB,CAAC,CAAC;IAErC;;;;;;;;OAQG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;;;;;;;;;OAUG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;CACtC;AAED,yBAAiB,mBAAmB,CAAC;IACnC;;OAEG;IACH,KAAY,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;IAE5C;;OAEG;IACH,KAAY,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC;IAExD;;OAEG;IACH,KAAY,aAAa,GAAG,WAAW,CAAC,aAAa,CAAC;IAEtD;;OAEG;IACH,KAAY,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC;IAExD,KAAY,+BAA+B,GAAG,wBAAwB,CAAC,+BAA+B,CAAC;IACvG,KAAY,4BAA4B,GAAG,wBAAwB,CAAC,4BAA4B,CAAC;CAClG;AAED,MAAM,WAAW,+BAAgC,SAAQ,uBAAuB;IAC9E;;;;;;OAMG;IACH,MAAM,CAAC,EAAE,KAAK,CAAC;CAChB;AAED,MAAM,WAAW,4BAA6B,SAAQ,uBAAuB;IAC3E;;;;;;OAMG;IACH,MAAM,EAAE,IAAI,CAAC;CACd;AAED,MAAM,CAAC,OAAO,WAAW,QAAQ,CAAC;IAChC,OAAO,EACL,KAAK,sCAAsC,IAAI,sCAAsC,EACrF,KAAK,gCAAgC,IAAI,gCAAgC,EACzE,KAAK,wBAAwB,IAAI,wBAAwB,EACzD,KAAK,6BAA6B,IAAI,6BAA6B,EACnE,KAAK,+BAA+B,IAAI,+BAA+B,EACvE,KAAK,qBAAqB,IAAI,qBAAqB,EACnD,KAAK,qCAAqC,IAAI,qCAAqC,EACnF,KAAK,kCAAkC,IAAI,kCAAkC,EAC7E,KAAK,sBAAsB,IAAI,sBAAsB,EACrD,KAAK,qCAAqC,IAAI,qCAAqC,EACnF,KAAK,sCAAsC,IAAI,sCAAsC,EACrF,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,+BAA+B,IAAI,+BAA+B,EACvE,KAAK,4BAA4B,IAAI,4BAA4B,GAClE,CAAC;CACH"}