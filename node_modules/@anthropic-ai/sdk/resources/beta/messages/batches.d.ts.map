{"version": 3, "file": "batches.d.ts", "sourceRoot": "", "sources": ["../../../src/resources/beta/messages/batches.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAEhD,OAAO,KAAK,IAAI,MAAM,eAAe,CAAC;AACtC,OAAO,KAAK,OAAO,MAAM,SAAS,CAAC;AACnC,OAAO,KAAK,eAAe,MAAM,YAAY,CAAC;AAC9C,OAAO,EAAE,IAAI,EAAE,KAAK,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAC5D,OAAO,EAAE,YAAY,EAAE,MAAM,kCAAkC,CAAC;AAGhE,qBAAa,OAAQ,SAAQ,WAAW;IACtC;;;;;;OAMG;IACH,MAAM,CAAC,MAAM,EAAE,iBAAiB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC;IAYnG;;;;OAIG;IACH,QAAQ,CACN,cAAc,EAAE,MAAM,EACtB,MAAM,CAAC,EAAE,mBAAmB,EAC5B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC;IACpC,QAAQ,CAAC,cAAc,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC;IAmBlG;;;OAGG;IACH,IAAI,CACF,MAAM,CAAC,EAAE,eAAe,EACxB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAE,gBAAgB,CAAC;IAC7D,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAE,gBAAgB,CAAC;IAmB/F;;;;;;;;;;OAUG;IACH,MAAM,CACJ,cAAc,EAAE,MAAM,EACtB,MAAM,CAAC,EAAE,iBAAiB,EAC1B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC;IACpC,MAAM,CAAC,cAAc,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC;IAmBhG;;;;;;OAMG;IACG,OAAO,CACX,cAAc,EAAE,MAAM,EACtB,MAAM,CAAC,EAAE,kBAAkB,EAC3B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,OAAO,CAAC,YAAY,CAAC,kCAAkC,CAAC,CAAC;IACtD,OAAO,CACX,cAAc,EAAE,MAAM,EACtB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,OAAO,CAAC,YAAY,CAAC,kCAAkC,CAAC,CAAC;CA6B7D;AAED,qBAAa,sBAAuB,SAAQ,IAAI,CAAC,gBAAgB,CAAC;CAAG;AAErE,MAAM,WAAW,gBAAgB;IAC/B;;;;OAIG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;;OAGG;IACH,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC;IAE3B;;;OAGG;IACH,mBAAmB,EAAE,MAAM,GAAG,IAAI,CAAC;IAEnC;;;OAGG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;;;;;OAMG;IACH,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IAExB;;;OAGG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,iBAAiB,EAAE,aAAa,GAAG,WAAW,GAAG,OAAO,CAAC;IAEzD;;;;;;OAMG;IACH,cAAc,EAAE,6BAA6B,CAAC;IAE9C;;;;;;OAMG;IACH,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC;IAE3B;;;;OAIG;IACH,IAAI,EAAE,eAAe,CAAC;CACvB;AAED,MAAM,WAAW,8BAA8B;IAC7C,IAAI,EAAE,UAAU,CAAC;CAClB;AAED,MAAM,WAAW,6BAA6B;IAC5C,KAAK,EAAE,OAAO,CAAC,iBAAiB,CAAC;IAEjC,IAAI,EAAE,SAAS,CAAC;CACjB;AAED,MAAM,WAAW,6BAA6B;IAC5C,IAAI,EAAE,SAAS,CAAC;CACjB;AAED,MAAM,WAAW,kCAAkC;IACjD;;;;;OAKG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;;;;;OAMG;IACH,MAAM,EAAE,sBAAsB,CAAC;CAChC;AAED,MAAM,WAAW,6BAA6B;IAC5C;;;;OAIG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;;;OAIG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;;;OAIG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;;;OAIG;IACH,SAAS,EAAE,MAAM,CAAC;CACnB;AAED;;;;;;GAMG;AACH,MAAM,MAAM,sBAAsB,GAC9B,+BAA+B,GAC/B,6BAA6B,GAC7B,8BAA8B,GAC9B,6BAA6B,CAAC;AAElC,MAAM,WAAW,+BAA+B;IAC9C,OAAO,EAAE,eAAe,CAAC,WAAW,CAAC;IAErC,IAAI,EAAE,WAAW,CAAC;CACnB;AAED,MAAM,WAAW,iBAAiB;IAChC;;;OAGG;IACH,QAAQ,EAAE,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAE3C;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;CACtC;AAED,yBAAiB,iBAAiB,CAAC;IACjC,UAAiB,OAAO;QACtB;;;;;WAKG;QACH,SAAS,EAAE,MAAM,CAAC;QAElB;;;;;WAKG;QACH,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,+BAA+B,EAAE,OAAO,CAAC,CAAC;KACxE;CACF;AAED,MAAM,WAAW,mBAAmB;IAClC;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;CACtC;AAED,MAAM,WAAW,eAAgB,SAAQ,UAAU;IACjD;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;CACtC;AAED,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;CACtC;AAED,MAAM,WAAW,kBAAkB;IACjC;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;CACtC;AAID,MAAM,CAAC,OAAO,WAAW,OAAO,CAAC;IAC/B,OAAO,EACL,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,8BAA8B,IAAI,8BAA8B,EACrE,KAAK,6BAA6B,IAAI,6BAA6B,EACnE,KAAK,6BAA6B,IAAI,6BAA6B,EACnE,KAAK,kCAAkC,IAAI,kCAAkC,EAC7E,KAAK,6BAA6B,IAAI,6BAA6B,EACnE,KAAK,sBAAsB,IAAI,sBAAsB,EACrD,KAAK,+BAA+B,IAAI,+BAA+B,EACvE,sBAAsB,IAAI,sBAAsB,EAChD,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,eAAe,IAAI,eAAe,EACvC,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,kBAAkB,IAAI,kBAAkB,GAC9C,CAAC;CACH"}