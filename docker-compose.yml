version: '3'

services:
  n8n:
    image: n8nio/n8n
    ports:
      - "5678:5678"
    env_file:
      - .env
    environment:
      - N8N_PORT=${N8N_PORT:-5678}
      - N8N_PROTOCOL=${N8N_PROTOCOL:-http}
      - NODE_ENV=${NODE_ENV:-production}
      # User Management Configuration
      - N8N_USER_MANAGEMENT_JWT_SECRET=${N8N_USER_MANAGEMENT_JWT_SECRET}
      - N8N_USER_MANAGEMENT_JWT_DURATION_HOURS=${N8N_USER_MANAGEMENT_JWT_DURATION_HOURS:-168}
      # Security settings
      - N8N_SECURE_COOKIE=${N8N_SECURE_COOKIE:-false}
    volumes:
      - ~/.n8n:/home/<USER>/.n8n
    restart: unless-stopped