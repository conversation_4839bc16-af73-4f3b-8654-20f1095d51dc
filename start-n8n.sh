#!/bin/bash

# Start n8n with automatic owner setup
# This script starts n8n and then sets up the owner account automatically

echo "🚀 Starting n8n with automatic setup..."

# Function to cleanup background processes
cleanup() {
    echo "🛑 Shutting down..."
    if [ ! -z "$N8N_PID" ]; then
        kill $N8N_PID 2>/dev/null
    fi
    if [ ! -z "$SETUP_PID" ]; then
        kill $SETUP_PID 2>/dev/null
    fi
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start n8n in the background
echo "📦 Starting n8n server..."
npm start &
N8N_PID=$!

# Wait a moment for n8n to start
sleep 5

# Run the setup script in the background
echo "⚙️  Running owner setup..."
node setup-n8n-owner.js &
SETUP_PID=$!

# Wait for setup to complete
wait $SETUP_PID

echo "✅ Setup complete! n8n is running with owner account configured."
echo "🌐 Access n8n at: http://localhost:5678"
echo "📧 Login with: ${N8N_OWNER_EMAIL:-opali<PERSON><PERSON><PERSON>@gmail.com}"
echo "🔑 Password: ${N8N_OWNER_PASSWORD:-camuga79}"

# Keep n8n running
wait $N8N_PID
