# n8n Auto-Setup

This project provides an automated setup for n8n with predefined owner credentials.

## Quick Start

### Option 1: Using Docker Compose (Recommended)

1. Start n8n with Docker Compose:
```bash
docker-compose up -d
```

2. Wait for n8n to start, then run the setup script:
```bash
npm run setup-owner
```

3. Access n8n at http://localhost:5678 and login with:
   - **Email**: <EMAIL>
   - **Password**: Camuga79

### Option 2: Using npm directly

1. Install dependencies:
```bash
npm install
```

2. Start n8n with automatic setup:
```bash
npm run start-with-setup
```

3. Access n8n at http://localhost:5678 and login with the credentials above.

### Option 3: Manual setup

1. Start n8n:
```bash
npm start
```

2. In another terminal, run the setup script:
```bash
npm run setup-owner
```

## Configuration

The credentials and configuration are stored in the `.env` file:

```env
N8N_OWNER_EMAIL=<EMAIL>
N8N_OWNER_PASSWORD=Camuga79
N8N_OWNER_FIRST_NAME=Owner
N8N_OWNER_LAST_NAME=User
```

You can modify these values in the `.env` file before starting n8n.

## Files

- `docker-compose.yml` - Docker Compose configuration for n8n
- `.env` - Environment variables including owner credentials
- `setup-n8n-owner.js` - Script to automatically create the owner account
- `start-n8n.sh` - Bash script to start n8n with automatic setup
- `package.json` - npm configuration with custom scripts

## Security Notes

⚠️ **Important**: The credentials are stored in plain text in the `.env` file. Make sure to:

1. Change the default JWT secret in production
2. Use strong passwords
3. Don't commit the `.env` file to version control if it contains sensitive data
4. Consider using environment variables or secrets management in production

## Troubleshooting

If the setup fails:

1. Make sure n8n is running and accessible at http://localhost:5678
2. Check if the owner account already exists
3. Verify the credentials in the `.env` file
4. Check the logs for any error messages

## Manual Owner Setup

If the automatic setup doesn't work, you can set up the owner manually:

1. Go to http://localhost:5678
2. Fill in the setup form with:
   - Email: <EMAIL>
   - Password: camuga79
   - First Name: Owner
   - Last Name: User
3. Click "Setup"
