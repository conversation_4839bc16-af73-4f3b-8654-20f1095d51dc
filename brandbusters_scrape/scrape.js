const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
const xml2js = require('xml2js');

// Read sitemap.xml
const sitemapContent = fs.readFileSync('../sitemap.xml', 'utf8');

// Function to extract URLs from sitemap
async function extractUrlsFromSitemap(content) {
  const parser = new xml2js.Parser({ explicitArray: false });
  const result = await parser.parseStringPromise(content);

  const urls = result.urlset.url.map(item => item.loc);
  return urls;
}

// Function to scrape text from a URL
async function scrapeUrl(page, url) {
  console.log(`Scraping: ${url}`);

  try {
    await page.goto(url, { waitUntil: 'networkidle', timeout: 60000 });

    // Extract all text content from the page
    const textContent = await page.evaluate(() => {
      // Get all text nodes
      const walker = document.createTreeWalker(
        document.body,
        NodeFilter.SHOW_TEXT,
        null,
        false
      );

      let text = '';
      let node;

      while (node = walker.nextNode()) {
        // Skip empty text nodes or those with just whitespace
        const trimmedContent = node.textContent.trim();
        if (trimmedContent) {
          text += trimmedContent + '\n';
        }
      }

      return text;
    });

    return {
      url,
      content: textContent
    };
  } catch (error) {
    console.error(`Error scraping ${url}:`, error.message);
    return {
      url,
      content: `[ERROR] Failed to scrape: ${error.message}`
    };
  }
}

// Function to find and scrape all links on a page
async function findAndScrapeLinks(page, baseUrl, scrapedUrls) {
  const links = await page.evaluate((baseUrl) => {
    const anchors = Array.from(document.querySelectorAll('a[href]'));
    return anchors
      .map(a => {
        let href = a.href;
        // Only include links from the same domain
        if (href.startsWith(baseUrl)) {
          return href;
        }
        return null;
      })
      .filter(href => href !== null);
  }, baseUrl);

  // Filter out already scraped URLs and duplicates
  const newLinks = [...new Set(links.filter(link => !scrapedUrls.has(link)))];

  return newLinks;
}

// Main function
async function main() {
  // Extract URLs from sitemap
  const sitemapUrls = await extractUrlsFromSitemap(sitemapContent);

  // Add blog URL
  sitemapUrls.push('https://brandbusters.net/blog');

  // Initialize browser
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  // Set to track scraped URLs
  const scrapedUrls = new Set();
  // Queue of URLs to scrape
  const urlQueue = [...sitemapUrls];
  // Results array
  const results = [];

  // Process URLs until queue is empty
  while (urlQueue.length > 0) {
    const url = urlQueue.shift();

    // Skip if already scraped
    if (scrapedUrls.has(url)) {
      continue;
    }

    // Mark as scraped
    scrapedUrls.add(url);

    // Scrape the URL
    const result = await scrapeUrl(page, url);
    results.push(result);

    // Find new links on the page and add them to the queue
    if (url.includes('brandbusters.net')) {
      const newLinks = await findAndScrapeLinks(page, 'https://brandbusters.net', scrapedUrls);
      urlQueue.push(...newLinks);
    }
  }

  // Close browser
  await browser.close();

  // Write results to file
  const outputPath = 'scraped_content.txt';

  let fullContent = '';
  for (const result of results) {
    fullContent += `\n\n=== URL: ${result.url} ===\n\n`;
    fullContent += result.content;
  }

  fs.writeFileSync(outputPath, fullContent);
  console.log(`Scraping complete. Results saved to ${outputPath}`);
}

main().catch(console.error);
