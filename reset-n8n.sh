#!/bin/bash

# Reset n8n data and start fresh with new owner credentials

echo "🔄 Resetting n8n data..."

# Stop any running n8n processes
echo "🛑 Stopping n8n processes..."
pkill -f "n8n start" || true

# Remove n8n data directory
echo "🗑️  Removing n8n data directory..."
rm -rf ~/.n8n

echo "✅ n8n data reset complete!"
echo "🚀 You can now start n8n with: npm run start-with-setup"
echo "📧 The owner will be set up with: opali<PERSON><PERSON><PERSON>@gmail.com"
echo "🔑 Password: camuga79"
