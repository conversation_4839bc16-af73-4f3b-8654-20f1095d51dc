#!/usr/bin/env node

/**
 * Test script to verify n8n login with the configured credentials
 */

const axios = require('axios');

const N8N_BASE_URL = process.env.N8N_BASE_URL || 'http://localhost:5678';
const OWNER_EMAIL = process.env.N8N_OWNER_EMAIL || '<EMAIL>';
const OWNER_PASSWORD = process.env.N8N_OWNER_PASSWORD || 'Camuga79';

async function testLogin() {
  try {
    console.log('🔐 Testing login with configured credentials...');

    const loginData = {
      emailOrLdapLoginId: OWNER_EMAIL,
      password: OWNER_PASSWORD
    };

    const response = await axios.post(`${N8N_BASE_URL}/rest/login`, loginData);

    if (response.status === 200) {
      console.log('✅ Login successful!');
      console.log(`📧 Email: ${OWNER_EMAIL}`);
      console.log(`🔑 Password: ${OWNER_PASSWORD}`);
      console.log('🎉 You can now access n8n with these credentials');
      return true;
    }
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('❌ Login failed - Invalid credentials');
      console.log('Please check your email and password');
    } else {
      console.error('❌ Login test failed:', error.response?.data || error.message);
    }
    return false;
  }
}

async function main() {
  try {
    // First check if n8n is running
    await axios.get(`${N8N_BASE_URL}/healthz`);
    console.log('✅ n8n is running');

    // Test login
    await testLogin();

  } catch (error) {
    console.error('❌ n8n is not running or not accessible');
    console.log('Please start n8n first with: npm start');
  }
}

if (require.main === module) {
  main();
}

module.exports = { testLogin };
