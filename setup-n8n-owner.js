#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to automatically set up the n8n instance owner
 * This script will create the initial owner account if it doesn't exist
 */

const axios = require('axios');

const N8N_BASE_URL = process.env.N8N_BASE_URL || 'http://localhost:5678';
const OWNER_EMAIL = process.env.N8N_OWNER_EMAIL || '<EMAIL>';
const OWNER_PASSWORD = process.env.N8N_OWNER_PASSWORD || 'Camuga79';
const OWNER_FIRST_NAME = process.env.N8N_OWNER_FIRST_NAME || 'Owner';
const OWNER_LAST_NAME = process.env.N8N_OWNER_LAST_NAME || 'User';

async function waitForN8n() {
  console.log('Waiting for n8n to be ready...');
  let attempts = 0;
  const maxAttempts = 30;

  while (attempts < maxAttempts) {
    try {
      await axios.get(`${N8N_BASE_URL}/healthz`);
      console.log('n8n is ready!');
      return true;
    } catch (error) {
      attempts++;
      console.log(`Attempt ${attempts}/${maxAttempts}: n8n not ready yet, waiting...`);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  throw new Error('n8n did not become ready in time');
}

async function checkOwnerSetup() {
  try {
    // Try to access the setup endpoint
    const response = await axios.get(`${N8N_BASE_URL}/rest/owner/setup`);
    // If we get a response, it means setup is needed
    return true;
  } catch (error) {
    if (error.response?.status === 404) {
      // 404 means the setup endpoint is not available, so owner is already set up
      console.log('Owner setup endpoint not found - owner already configured');
      return false;
    }
    // For other errors, assume setup is needed
    console.log('Error checking owner setup:', error.message);
    return true;
  }
}

async function setupOwner() {
  try {
    console.log('Setting up n8n owner account...');

    const setupData = {
      email: OWNER_EMAIL,
      password: OWNER_PASSWORD,
      firstName: OWNER_FIRST_NAME,
      lastName: OWNER_LAST_NAME,
      agree: true
    };

    const response = await axios.post(`${N8N_BASE_URL}/rest/owner/setup`, setupData);

    if (response.status === 200) {
      console.log('✅ Owner account created successfully!');
      console.log(`Email: ${OWNER_EMAIL}`);
      console.log(`Password: ${OWNER_PASSWORD}`);
      return true;
    }
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.message?.includes('already setup')) {
      console.log('ℹ️  Owner account already exists');
      return true;
    }
    console.error('❌ Error setting up owner account:', error.response?.data || error.message);
    return false;
  }
}

async function main() {
  try {
    await waitForN8n();

    // Always try to set up the owner - if it already exists, the API will tell us
    const setupResult = await setupOwner();

    if (!setupResult) {
      console.log('ℹ️  Checking if owner setup is needed...');
      const needsSetup = await checkOwnerSetup();

      if (needsSetup) {
        console.log('⚠️  Owner setup appears to be needed but setup failed');
        console.log('🌐 Please visit n8n manually to complete setup');
      }
    }

    console.log('\n🎉 n8n is ready to use!');
    console.log(`🌐 Access n8n at: ${N8N_BASE_URL}`);
    console.log(`📧 Login with: ${OWNER_EMAIL}`);
    console.log(`🔑 Password: ${OWNER_PASSWORD}`);

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main, setupOwner, checkOwnerSetup, waitForN8n };
